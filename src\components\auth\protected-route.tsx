import React from 'react'
import { useAuth } from '@/hooks/use-auth'
import { useNavigate } from '@tanstack/react-router'
import { Loader2 } from 'lucide-react'

/**
 * 路由保护组件属性
 */
interface ProtectedRouteProps {
  children: React.ReactNode
  redirectTo?: string
}

/**
 * 路由保护组件
 * 检查用户是否已登录，未登录则重定向到登录页面
 */
export function ProtectedRoute({ 
  children, 
  redirectTo = '/auth/login' 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const navigate = useNavigate()

  // 处理未登录状态的重定向 - 必须在所有条件渲染之前调用
  React.useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // 保存当前路径，登录后可以重定向回来
      const currentPath = window.location.pathname
      if (currentPath !== redirectTo) {
        localStorage.setItem('redirectAfterLogin', currentPath)
      }
      
      // 使用 navigate 进行重定向
      navigate({ to: redirectTo })
    }
  }, [isLoading, isAuthenticated, navigate, redirectTo])

  // 如果正在加载认证状态，显示加载界面
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    )
  }

  // 如果未登录，显示加载界面
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">正在跳转到登录页面...</p>
        </div>
      </div>
    )
  }

  // 已登录，渲染子组件
  return <>{children}</>
}
{"name": "sinochem-agent", "module": "index.ts", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "private": true, "devDependencies": {"@types/bun": "latest", "@types/jspdf": "^2.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/word-extractor": "^1.0.6", "drizzle-kit": "^0.31.4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "vite-tsconfig-paths": "^5.1.4"}, "peerDependencies": {"typescript": "^5.8.3"}, "dependencies": {"@libsql/client": "^0.15.10", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.129.7", "@tanstack/react-start": "^1.129.7", "@tanstack/router-devtools": "^1.130.2", "@types/bcryptjs": "^3.0.0", "@types/crypto-js": "^4.2.2", "@types/spark-md5": "^3.0.5", "@vitejs/plugin-react": "^4.7.0", "aliyun_numberauthsdk_web": "^2.1.12", "bcryptjs": "^3.0.2", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.3", "express": "^5.1.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.23.11", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "pdfjs-dist": "^5.3.93", "react": "^19.1.0", "react-dom": "^19.1.0", "react-tooltip": "^5.29.1", "spark-md5": "^3.0.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vite": "^7.0.5", "word-extractor": "^1.0.4", "zod": "^4.0.5", "zustand": "^5.0.6"}}
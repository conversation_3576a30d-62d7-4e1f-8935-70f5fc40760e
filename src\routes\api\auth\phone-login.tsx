import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'
import { getUserByMobile, createUserWithMobile } from '@/db/user'
import crypto from 'crypto'

/**
 * Phone number login API endpoint
 * Handles phone number authentication using <PERSON>yun spToken
 */
export const ServerRoute = createServerFileRoute('/api/auth/phone-login')
  .methods({
    POST: async ({ request }) => {
      try {
        const body = await request.json()
        const { spToken } = body

        // Validate required fields
        if (!spToken) {
          return json(
            { 
              error: 'Missing required fields',
              message: 'spToken为必填项'
            },
            { status: 400 }
          )
        }

        // 阿里云号码认证配置
        const accessKeyId = process.env.VITE_ALIYUN_ACCESS_KEY_ID
        const accessKeySecret = process.env.VITE_ALIYUN_ACCESS_KEY_SECRET

        if (!accessKeyId || !accessKeySecret) {
          return json(
            { 
              error: 'Missing configuration',
              message: '阿里云配置不完整'
            },
            { status: 500 }
          )
        }

        // 构建请求参数
        const timestamp = new Date().toISOString()
        const nonce = Math.random().toString(36).substring(2)

        const params = {
          'Action': 'GetPhoneWithToken',
          'Version': '2017-05-25',
          'RegionId': 'cn-hangzhou',
          'AccessKeyId': accessKeyId,
          'SignatureMethod': 'HMAC-SHA1',
          'Timestamp': timestamp,
          'SignatureVersion': '1.0',
          'SignatureNonce': nonce,
          'Format': 'JSON',
          'SpToken': spToken,
        }

        // 生成签名
        const sortedParams = Object.keys(params).sort().map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key as keyof typeof params])}`).join('&')
        const stringToSign = `POST&${encodeURIComponent('/')}&${encodeURIComponent(sortedParams)}`
        const signature = crypto.createHmac('sha1', accessKeySecret + '&').update(stringToSign).digest('base64')

        // 添加签名到参数
        const finalParams = { ...params, Signature: signature }

        // 调用阿里云GetPhoneWithToken接口获取手机号
        const phoneResponse = await fetch('https://dypnsapi.aliyuncs.com/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams(finalParams)
        })

        if (!phoneResponse.ok) {
          throw new Error(`阿里云API调用失败: ${phoneResponse.status}`)
        }

        const phoneData = await phoneResponse.json()

        if (phoneData.Code !== 'OK') {
          return json(
            { 
              error: 'Phone verification failed',
              message: phoneData.Message || '手机号验证失败'
            },
            { status: 401 }
          )
        }

        const phoneNumber = phoneData.GetPhoneWithTokenResponse?.PhoneNumber

        if (!phoneNumber) {
          return json(
            { 
              error: 'Phone number not found',
              message: '无法获取手机号'
            },
            { status: 401 }
          )
        }

        // 查找或创建用户
        let user = await getUserByMobile(phoneNumber)
        
        if (!user) {
          // 用户不存在，创建新用户
          user = await createUserWithMobile({
            mobile: phoneNumber,
            name: `用户${phoneNumber.slice(-4)}` // 使用手机号后4位作为默认用户名
          })
        }

        // Generate token
        const token = `auth_${user.id}_${Date.now()}`

        // Remove password from response
        const { password: _, ...userWithoutPassword } = user

        return json({
          success: true,
          message: '登录成功',
          user: userWithoutPassword,
          token
        })

      } catch (error: any) {
        console.error('Phone login error:', error)
        
        return json(
          { 
            error: 'Login failed',
            message: error.message || '登录失败，请稍后重试'
          },
          { status: 500 }
        )
      }
    }
  })

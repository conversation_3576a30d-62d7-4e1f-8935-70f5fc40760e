import{f as v,r as a,j as e}from"./main-B6mr56sS.js";import{c as k}from"./createLucideIcon-3aflogHk.js";import{U as C}from"./user-Dsb5yfVu.js";import{M as S}from"./mail-BJdA_1fC.js";import{L as x,E as p,a as h}from"./lock-Cwc0PnYk.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],R=k("user-plus",P),E=()=>{const l=v(),[s,b]=a.useState({email:"",password:"",confirmPassword:"",name:""}),[i,y]=a.useState(!1),[c,g]=a.useState(!1),[m,f]=a.useState(!1),[d,t]=a.useState(""),o=n=>{const{name:r,value:u}=n.target;b(N=>({...N,[r]:u})),d&&t("")},w=()=>!s.email||!s.password||!s.confirmPassword?(t("请填写所有必填字段"),!1):/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s.email)?s.password.length<6?(t("密码长度至少6位"),!1):s.password!==s.confirmPassword?(t("两次输入的密码不一致"),!1):!0:(t("邮箱格式不正确"),!1),j=async n=>{if(n.preventDefault(),!!w()){f(!0),t("");try{const r=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s.email,password:s.password,name:s.name||"新用户"})}),u=await r.json();r.ok?(alert("注册成功！请登录"),l.navigate({to:"/auth/login"})):t(u.message||"注册失败")}catch(r){console.error("Registration error:",r),t("网络错误，请稍后重试")}finally{f(!1)}}};return e.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1",children:e.jsxs("div",{className:"w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black",children:[e.jsx("div",{className:"flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5",children:e.jsx(R,{className:"w-7 h-7 text-black"})}),e.jsx("h2",{className:"text-2xl font-semibold mb-2 text-center",children:"用户注册"}),e.jsx("p",{className:"text-gray-500 text-sm mb-8 text-center",children:"创建您的账户"}),d&&e.jsx("div",{className:"w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm",children:d}),e.jsxs("form",{onSubmit:j,className:"w-full space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(C,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"text",name:"name",placeholder:"姓名（可选）",value:s.name,onChange:o,className:"w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(S,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",name:"email",placeholder:"邮箱地址",value:s.email,onChange:o,required:!0,className:"w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(x,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:i?"text":"password",name:"password",placeholder:"密码（至少6位）",value:s.password,onChange:o,required:!0,className:"w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("button",{type:"button",onClick:()=>y(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:i?e.jsx(p,{className:"w-5 h-5"}):e.jsx(h,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"relative",children:[e.jsx(x,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:c?"text":"password",name:"confirmPassword",placeholder:"确认密码",value:s.confirmPassword,onChange:o,required:!0,className:"w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("button",{type:"button",onClick:()=>g(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:c?e.jsx(p,{className:"w-5 h-5"}):e.jsx(h,{className:"w-5 h-5"})})]}),e.jsx("button",{type:"submit",disabled:m,className:"w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed",children:m?"注册中...":"注册"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-gray-500 text-sm",children:["已有账户？"," ",e.jsx("button",{onClick:()=>l.navigate({to:"/auth/login"}),className:"text-blue-500 hover:text-blue-600 font-medium",children:"立即登录"})]})})]})})},M=function(){return e.jsx(E,{})};export{M as component};

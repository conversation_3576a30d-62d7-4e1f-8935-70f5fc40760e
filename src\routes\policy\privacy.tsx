import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export const Route = createFileRoute('/policy/privacy')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <Button
        variant="ghost"
        className="mb-4"
        asChild
      >
        <Link to="/">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Link>
      </Button>
      <Card>
        <CardHeader>
          <CardTitle className="text-3xl font-bold">隐私政策</CardTitle>
          <CardDescription className="text-lg">
            最后更新日期：2024年12月
          </CardDescription>
        </CardHeader>
        <CardContent className="prose prose-gray max-w-none">
          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">1. 信息收集</h2>
            <p>我们收集以下类型的信息：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li><strong>账户信息：</strong>当您注册账户时，我们会收集您的姓名、电子邮件地址和密码</li>
              <li><strong>使用数据：</strong>我们会收集您如何使用我们服务的信息，包括交互记录和偏好设置</li>
              <li><strong>设备信息：</strong>我们可能会收集设备标识符、浏览器类型和IP地址</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">2. 信息使用</h2>
            <p>我们使用收集的信息来：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>提供、维护和改进我们的服务</li>
              <li>个性化您的用户体验</li>
              <li>与您沟通，包括发送服务更新和通知</li>
              <li>保护我们的服务和用户的安全</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">3. 信息共享</h2>
            <p>我们不会出售您的个人信息。我们仅在以下情况下共享信息：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>获得您的明确同意</li>
              <li>遵守法律义务或响应合法要求</li>
              <li>保护我们的权利、隐私、安全或财产</li>
              <li>与服务提供商合作，这些提供商必须保护您的信息</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">4. 数据安全</h2>
            <p>我们实施适当的技术和组织措施来保护您的个人信息，包括：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>使用加密技术保护数据传输</li>
              <li>实施访问控制和身份验证机制</li>
              <li>定期进行安全评估和审计</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">5. 您的权利</h2>
            <p>根据适用法律，您可能拥有以下权利：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>访问您的个人信息</li>
              <li>更正不准确的个人信息</li>
              <li>删除您的个人信息</li>
              <li>限制或反对处理您的个人信息</li>
              <li>数据可携带权</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">6. 数据保留</h2>
            <p>我们仅在实现本隐私政策所述目的所需的时间内保留您的个人信息，除非法律要求或允许更长的保留期。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">7. 第三方链接</h2>
            <p>我们的服务可能包含指向第三方网站或服务的链接。我们对这些第三方的隐私实践不承担责任，建议您查看他们的隐私政策。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">8. 儿童隐私</h2>
            <p>我们的服务不针对13岁以下的儿童。如果我们发现收集了13岁以下儿童的个人信息，我们将采取措施删除这些信息。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">9. 隐私政策的变更</h2>
            <p>我们可能会不时更新本隐私政策。任何变更都将在本页面发布，并在适当情况下通过其他方式通知您。</p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">10. 联系我们</h2>
            <p>如果您对本隐私政策有任何疑问或担忧，请通过以下方式联系我们：</p>
            <p className="mt-2">电子邮件：<a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a></p>
            <p>电话：<a href="tel:+861012345678" className="text-blue-600 hover:underline">+86 10 1234 5678</a></p>
          </section>
        </CardContent>
      </Card>
    </div>
  )
}

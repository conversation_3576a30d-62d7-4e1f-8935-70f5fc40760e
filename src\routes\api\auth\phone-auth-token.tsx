import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'
import crypto from 'crypto'

/**
 * Get <PERSON>yun phone auth token API endpoint
 * Handles getting accessToken and jwtToken for phone number authentication
 */
export const ServerRoute = createServerFileRoute('/api/auth/phone-auth-token')
  .methods({
    POST: async ({ request }) => {
      try {
        // 阿里云号码认证配置
        const accessKeyId = import.meta.env.VITE_ALIYUN_ACCESS_KEY_ID
        const accessKeySecret = import.meta.env.VITE_ALIYUN_ACCESS_KEY_SECRET
        const schemeCode = import.meta.env.VITE_ALIYUN_SCHEME_CODE

        if (!accessKeyId || !accessKeySecret || !schemeCode) {
          return json(
            {
              error: 'Missing configuration',
              message: '阿里云配置不完整，请检查环境变量'
            },
            { status: 500 }
          )
        }

        // 构建请求参数
        const timestamp = new Date().toISOString()
        const nonce = Math.random().toString(36).substring(2)

        const params = {
          'Action': 'GetAuthToken',
          'Version': '2017-05-25',
          'RegionId': 'cn-hangzhou',
          'AccessKeyId': accessKeyId,
          'SignatureMethod': 'HMAC-SHA1',
          'Timestamp': timestamp,
          'SignatureVersion': '1.0',
          'SignatureNonce': nonce,
          'Format': 'JSON',
          'SchemeCode': schemeCode,
        }

        // 生成签名
        const sortedParams = Object.keys(params).sort().map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key as keyof typeof params])}`).join('&')
        const stringToSign = `POST&${encodeURIComponent('/')}&${encodeURIComponent(sortedParams)}`
        const signature = crypto.createHmac('sha1', accessKeySecret + '&').update(stringToSign).digest('base64')

        // 添加签名到参数
        const finalParams = { ...params, Signature: signature }

        // 调用阿里云GetAuthToken接口
        const authTokenResponse = await fetch('https://dypnsapi.aliyuncs.com/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams(finalParams)
        })

        if (!authTokenResponse.ok) {
          throw new Error(`阿里云API调用失败: ${authTokenResponse.status}`)
        }

        const authTokenData = await authTokenResponse.json()

        if (authTokenData.Code !== 'OK') {
          throw new Error(`获取认证token失败: ${authTokenData.Message}`)
        }

        return json({
          success: true,
          accessToken: authTokenData.TokenInfo.AccessToken,
          jwtToken: authTokenData.TokenInfo.JwtToken,
        })

      } catch (error: any) {
        console.error('Phone auth token error:', error)
        
        return json(
          { 
            error: 'Auth token failed',
            message: error.message || '获取认证token失败，请稍后重试'
          },
          { status: 500 }
        )
      }
    }
  })

"use client";

import * as React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { User, LogOut } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { useAuth } from "@/hooks/use-auth";
import { useUserStore } from "@/stores/user-store";

interface UserAvatarProps {
	/** 自定义类名 */
	className?: string;
}

/**
 * 用户头像组件
 * 包含下拉菜单，支持跳转到账号管理页面
 */
export function UserAvatar({
	className,
}: UserAvatarProps) {
	const navigate = useNavigate();
	const { logout } = useAuth();
	const { user } = useUserStore();

	// 获取用户名首字母作为头像后备显示
	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((n) => n[0])
			.join("")
			.toUpperCase()
			.slice(0, 2);
	};

	// 处理账号管理点击
	const handleAccountManagement = () => {
		navigate({ to: "/user" });
	};



	// 处理登出
	const handleLogout = () => {
		logout();
		// 退出登录后跳转到登录页面
		navigate({ to: "/auth/login" });
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="ghost"
					size="sm"
					className={`h-8 w-8 rounded-full p-0 ${className}`}
				>
					<Avatar className="h-8 w-8">
						<AvatarImage src={user?.avatar || ""} alt={user?.name || "用户"} />
						<AvatarFallback className="text-xs">
							{getInitials(user?.name || "用户")}
						</AvatarFallback>
					</Avatar>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-56">
				<DropdownMenuLabel className="font-normal">
					<div className="flex flex-col space-y-1">
						<p className="text-sm font-medium leading-none">{user?.name || "用户"}</p>
					</div>
				</DropdownMenuLabel>
				<DropdownMenuSeparator />
				<DropdownMenuItem onClick={handleAccountManagement}>
					<User className="mr-2 h-4 w-4" />
					<span>账号管理</span>
				</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuItem onClick={handleLogout}>
					<LogOut className="mr-2 h-4 w-4" />
					<span>退出登录</span>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
import{r as o,j as d,c as O,e as T}from"./main-B6mr56sS.js";import{c as m}from"./index-_TRYHs0w.js";import{c as F}from"./button-B5f_3GyS.js";import{c as z}from"./createLucideIcon-3aflogHk.js";function ce(e,t){const a=o.createContext(t),n=r=>{const{children:c,...s}=r,u=o.useMemo(()=>s,Object.values(s));return d.jsx(a.Provider,{value:u,children:c})};n.displayName=e+"Provider";function i(r){const c=o.useContext(a);if(c)return c;if(t!==void 0)return t;throw new Error(`\`${r}\` must be used within \`${e}\``)}return[n,i]}function D(e,t=[]){let a=[];function n(r,c){const s=o.createContext(c),u=a.length;a=[...a,c];const l=f=>{const{scope:h,children:S,...v}=f,M=h?.[e]?.[u]||s,k=o.useMemo(()=>v,Object.values(v));return d.jsx(M.Provider,{value:k,children:S})};l.displayName=r+"Provider";function p(f,h){const S=h?.[e]?.[u]||s,v=o.useContext(S);if(v)return v;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${r}\``)}return[l,p]}const i=()=>{const r=a.map(c=>o.createContext(c));return function(s){const u=s?.[e]||r;return o.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return i.scopeName=e,[n,H(i,...t)]}function H(...e){const t=e[0];if(e.length===1)return t;const a=()=>{const n=e.map(i=>({useScope:i(),scopeName:i.scopeName}));return function(r){const c=n.reduce((s,{useScope:u,scopeName:l})=>{const f=u(r)[`__scope${l}`];return{...s,...f}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return a.scopeName=t.scopeName,a}function q(e){const t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...a)=>t.current?.(...a),[])}var g=globalThis?.document?o.useLayoutEffect:()=>{},B=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],x=B.reduce((e,t)=>{const a=F(`Primitive.${t}`),n=o.forwardRef((i,r)=>{const{asChild:c,...s}=i,u=c?a:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),d.jsx(u,{...s,ref:r})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function ue(e,t){e&&O.flushSync(()=>e.dispatchEvent(t))}var V=T();function G(){return V.useSyncExternalStore(K,()=>!0,()=>!1)}function K(){return()=>{}}var w="Avatar",[U,de]=D(w),[W,E]=U(w),y=o.forwardRef((e,t)=>{const{__scopeAvatar:a,...n}=e,[i,r]=o.useState("idle");return d.jsx(W,{scope:a,imageLoadingStatus:i,onImageLoadingStatusChange:r,children:d.jsx(x.span,{...n,ref:t})})});y.displayName=w;var C="AvatarImage",L=o.forwardRef((e,t)=>{const{__scopeAvatar:a,src:n,onLoadingStatusChange:i=()=>{},...r}=e,c=E(C,a),s=J(n,r),u=q(l=>{i(l),c.onImageLoadingStatusChange(l)});return g(()=>{s!=="idle"&&u(s)},[s,u]),s==="loaded"?d.jsx(x.img,{...r,ref:t,src:n}):null});L.displayName=C;var R="AvatarFallback",b=o.forwardRef((e,t)=>{const{__scopeAvatar:a,delayMs:n,...i}=e,r=E(R,a),[c,s]=o.useState(n===void 0);return o.useEffect(()=>{if(n!==void 0){const u=window.setTimeout(()=>s(!0),n);return()=>window.clearTimeout(u)}},[n]),c&&r.imageLoadingStatus!=="loaded"?d.jsx(x.span,{...i,ref:t}):null});b.displayName=R;function A(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function J(e,{referrerPolicy:t,crossOrigin:a}){const n=G(),i=o.useRef(null),r=n?(i.current||(i.current=new window.Image),i.current):null,[c,s]=o.useState(()=>A(r,e));return g(()=>{s(A(r,e))},[r,e]),g(()=>{const u=f=>()=>{s(f)};if(!r)return;const l=u("loaded"),p=u("error");return r.addEventListener("load",l),r.addEventListener("error",p),t&&(r.referrerPolicy=t),typeof a=="string"&&(r.crossOrigin=a),()=>{r.removeEventListener("load",l),r.removeEventListener("error",p)}},[r,a,t]),c}var P=y,_=L,j=b;const Q=o.forwardRef(({className:e,...t},a)=>d.jsx(P,{ref:a,className:m("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));Q.displayName=P.displayName;const X=o.forwardRef(({className:e,...t},a)=>d.jsx(_,{ref:a,className:m("aspect-square h-full w-full",e),...t}));X.displayName=_.displayName;const Y=o.forwardRef(({className:e,...t},a)=>d.jsx(j,{ref:a,className:m("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));Y.displayName=j.displayName;var Z="Separator",N="horizontal",ee=["horizontal","vertical"],I=o.forwardRef((e,t)=>{const{decorative:a,orientation:n=N,...i}=e,r=te(n)?n:N,s=a?{role:"none"}:{"aria-orientation":r==="vertical"?r:void 0,role:"separator"};return d.jsx(x.div,{"data-orientation":r,...s,...i,ref:t})});I.displayName=Z;function te(e){return ee.includes(e)}var $=I;const re=o.forwardRef(({className:e,orientation:t="horizontal",decorative:a=!0,...n},i)=>d.jsx($,{ref:i,decorative:a,orientation:t,className:m("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...n}));re.displayName=$.displayName;/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],le=z("loader-circle",ae);export{Q as A,le as L,x as P,re as S,X as a,Y as b,q as c,ue as d,D as e,ce as f,g as u};

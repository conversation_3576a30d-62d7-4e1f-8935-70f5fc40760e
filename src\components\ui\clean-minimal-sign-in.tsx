"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { LogIn, Smartphone } from "lucide-react";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@/hooks/use-auth";
import { useUserStore } from "@/stores/user-store";
import { PhoneNumberServer } from 'aliyun_numberauthsdk_web';

/**
 * 简洁的登录组件
 * 支持钉钉登录和手机号一键登录
 */
const SignIn = () => {
	const router = useRouter();
	const { login } = useAuth();
	const { setUser } = useUserStore();
	const [loginMethod, setLoginMethod] = useState<'dingtalk' | 'phone'>('phone');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [phoneNumberServer, setPhoneNumberServer] = useState<any>(null);
	// 初始化手机号认证SDK
	useEffect(() => {
		if (typeof window !== 'undefined') {
			const server = new PhoneNumberServer();
			setPhoneNumberServer(server);
		}
	}, []);

	/**
	 * 获取阿里云认证token
	 */
	const getAuthTokens = async () => {
		try {
			const response = await fetch("/api/auth/phone-auth-token", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
			});

			const data = await response.json();

			if (response.ok) {
				return {
					accessToken: data.accessToken,
					jwtToken: data.jwtToken
				};
			} else {
				throw new Error(data.message || "获取认证token失败");
			}
		} catch (error) {
			console.error("Get auth tokens error:", error);
			throw error;
		}
	};

	/**
	 * 处理手机号一键登录
	 */
	const handlePhoneLogin = async () => {
		if (!phoneNumberServer) {
			setError("SDK未初始化，请刷新页面重试");
			return;
		}

		setIsLoading(true);
		setError("");

		try {
			// 检查网络类型
			const netType = phoneNumberServer.getConnection();
			if (netType === 'wifi') {
				setError("请关闭Wi-Fi并使用移动数据网络进行一键登录");
				setIsLoading(false);
				return;
			}

			// 获取认证token
			const { accessToken, jwtToken } = await getAuthTokens();

			// 身份鉴权
			phoneNumberServer.checkLoginAvailable({
				accessToken: accessToken,
				jwtToken: jwtToken,
				success: function (res: any) {
					// 身份鉴权成功，获取登录token
					phoneNumberServer.getLoginToken({
						success: function (res: any) {
							// 成功获取spToken，调用后端登录接口
							handlePhoneLoginWithToken(res.spToken);
						},
						error: function (res: any) {
							console.error("获取登录token失败:", res);
							setError("获取登录token失败，请重试");
							setIsLoading(false);
						},
						watch: function (status: string, data: any) {
							console.log("授权页状态:", status, data);
						},
						authPageOption: {
							// 授权页面配置
							navTitle: "一键登录",
							logoImg: "/logo.png",
							privacyAlertConfig: {
								privacyAlertIsNeedShow: true,
								privacyAlertIsNeedAutoLogin: false,
								privacyAlertMaskIsNeedShow: true,
							}
						}
					});
				},
				error: function (res: any) {
					console.error("身份鉴权失败:", res);
					setError("身份鉴权失败，请确保使用移动数据网络");
					setIsLoading(false);
				}
			});

		} catch (error) {
			console.error("Phone login error:", error);
			setError(error instanceof Error ? error.message : "一键登录失败，请重试");
			setIsLoading(false);
		}
	};

	/**
	 * 使用spToken进行登录
	 */
	const handlePhoneLoginWithToken = async (spToken: string) => {
		try {
			const response = await fetch("/api/auth/phone-login", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ spToken }),
			});

			const data = await response.json();

			if (response.ok) {
				// 登录成功
				login(data.user, data.token);
				setUser(data.user);

				// 关闭授权页面
				if (phoneNumberServer) {
					phoneNumberServer.closeLoginPage();
				}

				router.navigate({ to: "/" });
			} else {
				setError(data.message || "登录失败");
			}
		} catch (error) {
			console.error("Phone login with token error:", error);
			setError("网络错误，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	/**
	 * 处理钉钉登录
	 */
	const handleDingTalkLogin = () => {
		// 钉钉应用配置
		const clientId = import.meta.env.VITE_DINGTALK_CLIENT_ID || "your_client_id";
		const redirectUri = encodeURIComponent(
			window.location.origin + "/auth/dingtalk/callback"
		);
		const state = "dingtalk_login";

		if (!clientId || clientId === "your_client_id") {
			alert("钉钉应用配置不完整，请检查环境变量 VITE_DINGTALK_CLIENT_ID");
			return;
		}

		// 构建钉钉OAuth2.0授权URL
		const authUrl = `https://login.dingtalk.com/oauth2/auth?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=openid%20corpid&prompt=consent`;

		// 跳转到钉钉授权页面
		window.location.href = authUrl;
	};

	return (
		<div className="min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1">
			<div className="w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black">
				{/* 登录图标 */}
				<div className="flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5">
					<LogIn className="w-7 h-7 text-black" />
				</div>

				{/* 标题和描述 */}
				<h2 className="text-2xl font-semibold mb-2 text-center">用户登录</h2>
				<p className="text-gray-500 text-sm mb-6 text-center">
					选择您的登录方式
				</p>

				{/* 登录方式切换 */}
				<div className="w-full flex mb-6 bg-gray-100 rounded-xl p-1">
					<button
						onClick={() => setLoginMethod('phone')}
						className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition ${
							loginMethod === 'phone'
								? 'bg-white text-black shadow-sm'
								: 'text-gray-600 hover:text-black'
						}`}
					>
						手机号登录
					</button>
					<button
						onClick={() => setLoginMethod('dingtalk')}
						className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition relative ${
							loginMethod === 'dingtalk'
								? 'bg-white text-black shadow-sm'
								: 'text-gray-600 hover:text-black'
						}`}
					>
						钉钉登录
						<span className="absolute -top-2 right-1 px-1.5 py-0.5 bg-red-500 text-white text-xxs rounded-full" style={{ fontSize: '0.6rem' }}>
							仅限中化兴海内部成员
						</span>
					</button>
				</div>

				{/* Error message */}
				{error && (
					<div className="w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm">
						{error}
					</div>
				)}

				{/* 手机号一键登录 */}
				{loginMethod === 'phone' && (
					<div className="w-full space-y-4">
						{/* 网络提示 */}
						<div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-700">
							<div className="flex items-center gap-2 mb-2">
								<Smartphone className="w-4 h-4" />
								<span className="font-medium">使用提示</span>
							</div>
							<ul className="space-y-1 text-xs">
								<li>• 请确保关闭Wi-Fi，使用移动数据网络</li>
								<li>• 支持中国移动、中国联通、中国电信</li>
								<li>• 点击登录后需要输入手机号中间4位数字</li>
							</ul>
						</div>

						{/* 一键登录按钮 */}
						<button
							onClick={handlePhoneLogin}
							disabled={isLoading}
							className="w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
						>
							<Smartphone className="w-5 h-5" />
							{isLoading ? "登录中..." : "手机号一键登录"}
						</button>
					</div>
				)}

				{/* 钉钉登录 */}
				{loginMethod === 'dingtalk' && (
					<button
						onClick={handleDingTalkLogin}
						className="w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition mb-6 flex items-center justify-center gap-2"
					>
						<svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
							<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
						</svg>
						使用钉钉登录
					</button>
				)}

				{/* 注册提示 */}
				{loginMethod === 'phone' && (
					<div className="mt-6 text-center">
						<p className="text-gray-500 text-sm">
							首次使用将自动创建账户
						</p>
					</div>
				)}

				{/* 帮助信息 */}
				<div className="text-center text-sm text-gray-500 mt-4">
					登录即表示您同意我们的{" "}
					<a
						href="/terms"
						className="text-blue-600 hover:underline font-medium"
					>
						服务条款
					</a>{" "}
					和{" "}
					<a
						href="/privacy"
						className="text-blue-600 hover:underline font-medium"
					>
						隐私政策
					</a>
				</div>
			</div>
		</div>
	);
};

export { SignIn };

"use client";

import React, {
	useRef,
	useEffect,
	useState,
	useCallback,
	type Dispatch,
	type SetStateAction,
	type ChangeEvent,
	memo,
} from "react";

import equal from "fast-deep-equal";
import { AnimatePresence, motion } from "framer-motion";
import { Loader2 as LoaderIcon, X as XIcon } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";
import { twMerge } from "tailwind-merge";

const clsx = (...args: any[]) => args.filter(Boolean).join(" ");

// Type Definitions
interface Attachment {
	url: string;
	name: string;
	contentType: string;
	size: number;
}

interface UIMessage {
	id: string;
	content: string;
	role: string;
	attachments?: Attachment[];
}

type VisibilityType = "public" | "private" | "unlisted" | string;

// Utility Functions
const cn = (...inputs: any[]) => {
	return twMerge(clsx(inputs));
};

// Button variants using cva
const buttonVariants = cva(
	"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
	{
		variants: {
			variant: {
				// Primary: black background, white text
				default: "bg-black text-white hover:bg-gray-800",
				// Destructive: high-contrast gray outline, black text
				destructive: "border border-black text-black hover:bg-gray-100",
				// Outline: grayscale border, white background, black text
				outline:
					"border border-gray-400 bg-white hover:bg-gray-100 hover:text-black",
				// Secondary: grayscale background, gray text
				secondary: "bg-gray-200 text-black hover:bg-gray-300",
				// Ghost: hover effect, default text color (should be black)
				ghost: "text-black hover:bg-gray-100 hover:text-black", // Explicitly set text to black
				// Link: black text
				link: "text-black underline-offset-4 hover:underline",
			},
			size: {
				default: "h-10 px-4 py-2",
				sm: "h-9 rounded-md px-3",
				lg: "h-11 rounded-md px-8",
				icon: "h-10 w-10",
			},
		},
		defaultVariants: {
			variant: "default",
		},
	}
);

// Button component
interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement>,
		VariantProps<typeof buttonVariants> {
	asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant, size, asChild = false, ...props }, ref) => {
		const Comp = asChild ? "button" : "button";

		return (
			<Comp
				className={cn(buttonVariants({ variant, size, className }))}
				ref={ref}
				{...props}
			/>
		);
	}
);
Button.displayName = "Button";

// Textarea component
const Textarea = React.forwardRef<
	HTMLTextAreaElement,
	React.ComponentProps<"textarea">
>(({ className, ...props }, ref) => {
	return (
		<textarea
			className={cn(
				// Adjusted text color, placeholder color, and border/ring colors to grayscale
				"flex min-h-[80px] w-full rounded-md border border-gray-400 bg-white px-3 py-2 text-base ring-offset-white placeholder:text-black focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-black", // Added text-black
				className
			)}
			ref={ref}
			{...props}
		/>
	);
});
Textarea.displayName = "Textarea";

// Stop Icon SVG (uses currentColor)
const StopIcon = ({ size = 16 }: { size?: number }) => {
	return (
		<svg
			height={size}
			viewBox="0 0 16 16"
			width={size}
			style={{ color: "currentcolor" }}
		>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M3 3H13V13H3V3Z"
				fill="currentColor"
			/>
		</svg>
	);
};

// Arrow Up Icon SVG (Send) (uses currentColor)
const ArrowUpIcon = ({ size = 16 }: { size?: number }) => {
	return (
		<svg
			height={size}
			strokeLinejoin="round"
			viewBox="0 0 16 16"
			width={size}
			style={{ color: "currentcolor" }}
		>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z"
				fill="currentColor"
			/>
		</svg>
	);
};

// Sub-Components

interface SuggestedActionsProps {
	chatId: string;
	onSelectAction: (action: string) => void;
	selectedVisibilityType: VisibilityType;
}

/**
 * 建议操作组件 - 显示预设的聊天建议
 */
function PureSuggestedActions({ onSelectAction }: SuggestedActionsProps) {
	const suggestedActions = [
		{
			title: "受限空间内气体环境满足作业要求下",
			label: "气体检测分析合格标准是多少",
			action: "受限空间内气体环境满足作业要求下，气体检测分析合格标准是多少",
		},
		{
			title: "受限空间作业前,应根据受限空间",
			label:
				"盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准",
			action:
				"受限空间作业前,应根据受限空间盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准",
		},
		{
			title: "固定式配电箱及开关箱的底面",
			label: "离地面垂直高度的高度是多少",
			action: "固定式配电箱及开关箱的底面离地面垂直高度的高度是多少",
		},
		{
			title: "八大保命原则",
			label: "是什么",
			action: "中化集团八大保命原则",
		},
	];

	return (
		<div
			data-testid="suggested-actions"
			className="grid pb-2 grid-cols-1 sm:grid-cols-2 gap-2 w-full"
		>
			<AnimatePresence>
				{suggestedActions.map((suggestedAction, index) => (
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: 20 }}
						transition={{ delay: 0.05 * index }}
						key={`suggested-action-${index}`}
						className="block"
					>
						<Button
							variant="ghost"
							onClick={() => onSelectAction(suggestedAction.action)}
							className="text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start
                       border-gray-300 bg-white hover:bg-gray-100 text-black hover:text-gray-900"
						>
							<span className="font-medium">{suggestedAction.title}</span>
							<span className="text-gray-500">{suggestedAction.label}</span>
						</Button>
					</motion.div>
				))}
			</AnimatePresence>
		</div>
	);
}

const SuggestedActions = memo(PureSuggestedActions, (prevProps, nextProps) => {
	if (prevProps.chatId !== nextProps.chatId) return false;
	if (prevProps.selectedVisibilityType !== nextProps.selectedVisibilityType)
		return false;
	return true;
});

/**
 * 附件预览组件
 */
const PreviewAttachment = ({
	attachment,
	isUploading = false,
}: {
	attachment: Attachment;
	isUploading?: boolean;
}) => {
	const { name, url, contentType } = attachment;

	return (
		<div data-testid="input-attachment-preview" className="flex flex-col gap-1">
			<div className="w-20 h-16 aspect-video bg-gray-200 rounded-md relative flex flex-col items-center justify-center overflow-hidden border border-gray-300">
				{contentType?.startsWith("image/") && url ? (
					// eslint-disable-next-line @next/next/no-img-element
					<img
						key={url}
						src={url}
						alt={name ?? "An image attachment"}
						className="rounded-md size-full object-cover grayscale"
					/>
				) : (
					<div className="flex items-center justify-center text-xs text-gray-600 text-center p-1">
						File: {name?.split(".").pop()?.toUpperCase() || "Unknown"}
					</div>
				)}

				{isUploading && (
					<div
						data-testid="input-attachment-loader"
						className="animate-spin absolute text-gray-500"
					>
						<LoaderIcon className="size-5" />
					</div>
				)}
			</div>
			<div className="text-xs text-gray-600 max-w-20 truncate">{name}</div>
		</div>
	);
};

/**
 * 停止按钮组件
 */
function PureStopButton({ onStop }: { onStop: () => void }) {
	return (
		<Button
			data-testid="stop-button"
			// Using default variant (bg-black) and setting text color to white for the icon
			className="rounded-full p-1.5 h-fit border border-black text-white" // Added text-white
			onClick={(event) => {
				event.preventDefault();
				onStop();
			}}
			aria-label="Stop generating"
		>
			<StopIcon size={14} />
		</Button>
	);
}

const StopButton = memo(
	PureStopButton,
	(prev, next) => prev.onStop === next.onStop
);

/**
 * 发送按钮组件
 */
function PureSendButton({
	submitForm,
	input,
	uploadQueue,
	attachments,
	canSend,
	isGenerating,
}: {
	submitForm: () => void;
	input: string;
	uploadQueue: Array<string>;
	attachments: Array<Attachment>;
	canSend: boolean;
	isGenerating: boolean;
}) {
	const isDisabled =
		uploadQueue.length > 0 ||
		!canSend ||
		isGenerating ||
		(input.trim().length === 0 && attachments.length === 0);

	return (
		<Button
			data-testid="send-button"
			// Uses default variant (bg-black text-white)
			className="rounded-full p-1.5 h-fit"
			onClick={(event) => {
				event.preventDefault();
				if (!isDisabled) {
					submitForm();
				}
			}}
			disabled={isDisabled}
			aria-label="Send message"
		>
			<ArrowUpIcon size={14} />
		</Button>
	);
}

const SendButton = memo(PureSendButton, (prevProps, nextProps) => {
	if (prevProps.input !== nextProps.input) return false;
	if (prevProps.uploadQueue.length !== nextProps.uploadQueue.length)
		return false;
	if (prevProps.attachments.length !== nextProps.attachments.length)
		return false;
	if (
		prevProps.attachments.length > 0 &&
		!equal(prevProps.attachments, nextProps.attachments)
	)
		return false;
	if (prevProps.canSend !== nextProps.canSend) return false;
	if (prevProps.isGenerating !== nextProps.isGenerating) return false;
	return true;
});

// Main Component

interface MultimodalInputProps {
	chatId: string;
	messages: Array<UIMessage>;
	attachments: Array<Attachment>;
	setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
	onSendMessage: (params: { input: string; attachments: Attachment[] }) => void;
	onStopGenerating: () => void;
	isGenerating: boolean;
	canSend: boolean;
	className?: string;
	selectedVisibilityType: VisibilityType;
}

/**
 * 多模态输入组件 - 支持文本和文件输入的聊天界面
 */
function PureMultimodalInput({
	chatId,
	messages,
	attachments,
	setAttachments,
	onSendMessage,
	onStopGenerating,
	isGenerating,
	canSend,
	className,
	selectedVisibilityType,
}: MultimodalInputProps) {
	const textareaRef = useRef<HTMLTextAreaElement>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [input, setInput] = useState("");
	const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);

	/**
	 * 调整文本框高度
	 */
	const adjustHeight = () => {
		const textarea = textareaRef.current;
		if (textarea) {
			textarea.style.height = "auto";
			textarea.style.height = `${textarea.scrollHeight + 2}px`;
		}
	};

	/**
	 * 重置文本框高度
	 */
	const resetHeight = useCallback(() => {
		const textarea = textareaRef.current;
		if (textarea) {
			textarea.style.height = "auto";
			textarea.rows = 1;
			adjustHeight();
		}
	}, []);

	useEffect(() => {
		if (textareaRef.current) {
			adjustHeight();
		}
	}, [input]); // Depend only on input

	/**
	 * 处理输入变化
	 */
	const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
		setInput(event.target.value);
	};

	/**
	 * 模拟文件上传函数
	 */
	const uploadFile = async (file: File): Promise<Attachment | undefined> => {
		console.log(`MOCK: Simulating upload for file: ${file.name}`);
		return new Promise((resolve) => {
			setTimeout(() => {
				try {
					// Use URL.createObjectURL for client-side preview. Remember to revoke!
					const mockUrl = URL.createObjectURL(file);
					const mockAttachment: Attachment = {
						url: mockUrl,
						name: file.name,
						contentType: file.type || "application/octet-stream",
						size: file.size,
					};
					console.log(`MOCK: Upload successful for ${file.name}`);
					resolve(mockAttachment);
				} catch (error) {
					console.error(
						"MOCK: Failed to create object URL for preview:",
						error
					);
					resolve(undefined);
				} finally {
					// Remove file name from upload queue
					setUploadQueue((currentQueue) =>
						currentQueue.filter((name) => name !== file.name)
					);
				}
			}, 700); // Simulate delay
		});
	};

	/**
	 * 处理文件选择
	 */
	const handleFileChange = useCallback(
		async (event: ChangeEvent<HTMLInputElement>) => {
			const files = Array.from(event.target.files || []);
			if (files.length === 0) return;

			// Add files to upload queue immediately by name
			setUploadQueue((currentQueue) => [
				...currentQueue,
				...files.map((file) => file.name),
			]);

			// Clear the file input value to allow selecting the same file again
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}

			const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25 MB
			const validFiles = files.filter((file) => file.size <= MAX_FILE_SIZE);
			const invalidFiles = files.filter((file) => file.size > MAX_FILE_SIZE);

			if (invalidFiles.length > 0) {
				console.warn(
					`Skipped ${invalidFiles.length} files larger than ${
						MAX_FILE_SIZE / 1024 / 1024
					}MB.`
				);
				// Also remove invalid files from the upload queue
				setUploadQueue((currentQueue) =>
					currentQueue.filter(
						(name) => !invalidFiles.some((f) => f.name === name)
					)
				);
			}

			// Start uploads for valid files
			const uploadPromises = validFiles.map((file) => uploadFile(file));
			const uploadedAttachments = await Promise.all(uploadPromises);

			const successfullyUploadedAttachments = uploadedAttachments.filter(
				(attachment): attachment is Attachment => attachment !== undefined
			);

			// Add successfully uploaded attachments to the main attachments list
			setAttachments((currentAttachments) => [
				...currentAttachments,
				...successfullyUploadedAttachments,
			]);
		},
		[setAttachments, uploadFile]
	);

	/**
	 * 移除附件
	 */
	const handleRemoveAttachment = useCallback(
		(attachmentToRemove: Attachment) => {
			// Revoke the object URL
			if (attachmentToRemove.url.startsWith("blob:")) {
				URL.revokeObjectURL(attachmentToRemove.url);
			}
			// Filter out the attachment
			setAttachments((currentAttachments) =>
				currentAttachments.filter(
					(attachment) =>
						attachment.url !== attachmentToRemove.url ||
						attachment.name !== attachmentToRemove.name
				)
			);
			// Focus the textarea
			textareaRef.current?.focus();
		},
		[setAttachments, textareaRef]
	);

	/**
	 * 提交表单
	 */
	const submitForm = useCallback(() => {
		if (input.trim().length === 0 && attachments.length === 0) {
			console.warn("Please enter a message or add an attachment.");
			return;
		}

		onSendMessage({ input, attachments });

		// Clear input and attachments
		setInput("");
		setAttachments([]);

		// Revoke object URLs for sent attachments
		attachments.forEach((att) => {
			if (att.url.startsWith("blob:")) {
				URL.revokeObjectURL(att.url);
			}
		});

		resetHeight();
		textareaRef.current?.focus();
	}, [
		input,
		attachments,
		onSendMessage,
		setAttachments,
		textareaRef,
		resetHeight,
	]);

	const showSuggestedActions =
		messages.length === 0 &&
		attachments.length === 0 &&
		uploadQueue.length === 0;

	const isAttachmentDisabled = isGenerating || uploadQueue.length > 0;

	return (
		<div className={cn("relative w-full flex flex-col gap-4", className)}>
			<AnimatePresence>
				{showSuggestedActions && (
					<motion.div
						key="suggested-actions-container"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: 20 }}
						transition={{ duration: 0.2 }}
					>
						<SuggestedActions
							onSelectAction={(action) => {
								setInput(action);
								requestAnimationFrame(() => {
									adjustHeight();
									textareaRef.current?.focus();
								});
							}}
							chatId={chatId}
							selectedVisibilityType={selectedVisibilityType}
						/>
					</motion.div>
				)}
			</AnimatePresence>

			{/* Hidden file input */}
			<input
				type="file"
				className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
				ref={fileInputRef}
				multiple
				onChange={handleFileChange}
				tabIndex={-1}
				disabled={isAttachmentDisabled}
				accept="image/*,video/*,audio/*,.pdf" // Example mime types
			/>

			{(attachments.length > 0 || uploadQueue.length > 0) && (
				<div
					data-testid="attachments-preview"
					className="flex pt-[10px] flex-row gap-3 overflow-x-auto items-end pb-2 pl-1"
				>
					{attachments.map((attachment) => (
						<div
							key={attachment.url || attachment.name}
							className="relative group"
						>
							<PreviewAttachment attachment={attachment} isUploading={false} />
							<Button
								variant="destructive"
								size="icon"
								className="absolute top-[-8px] right-[-8px] h-5 w-5 rounded-full p-0 flex items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-opacity"
								onClick={() => handleRemoveAttachment(attachment)}
								aria-label={`Remove ${attachment.name}`}
							>
								<XIcon className="size-3" />
							</Button>
						</div>
					))}
					{uploadQueue.map((filename, index) => (
						<PreviewAttachment
							key={`upload-${filename}-${index}`}
							attachment={{
								url: "",
								name: filename,
								contentType: "",
								size: 0,
							}}
							isUploading={true}
						/>
					))}
				</div>
			)}

			<Textarea
				data-testid="multimodal-input"
				ref={textareaRef}
				placeholder="你想问什么"
				value={input}
				onChange={handleInput}
				className={cn(
					"min-h-[24px] max-h-[calc(75dvh)] overflow-y-auto resize-none rounded-2xl !text-base pb-10",
					"bg-gray-100 border border-gray-300",
					className
				)}
				style={{ color: "black" }}
				rows={1}
				autoFocus
				disabled={!canSend || isGenerating || uploadQueue.length > 0}
				onKeyDown={(event) => {
					if (
						event.key === "Enter" &&
						!event.shiftKey &&
						!event.nativeEvent.isComposing
					) {
						event.preventDefault();

						const canSubmit =
							canSend &&
							!isGenerating &&
							uploadQueue.length === 0 &&
							(input.trim().length > 0 || attachments.length > 0);

						if (canSubmit) {
							submitForm();
						}
					}
				}}
			/>

			<div className="absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end z-10">
				{isGenerating ? (
					<StopButton onStop={onStopGenerating} />
				) : (
					<SendButton
						submitForm={submitForm}
						input={input}
						uploadQueue={uploadQueue}
						attachments={attachments}
						canSend={canSend}
						isGenerating={isGenerating}
					/>
				)}
			</div>
		</div>
	);
}

export {
	PureMultimodalInput,
	type Attachment,
	type UIMessage,
	type VisibilityType,
};

import{j as o}from"./main-B6mr56sS.js";import{c as n,a}from"./index-_TRYHs0w.js";const s=a("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function u({className:r,variant:e,...t}){return o.jsx("div",{className:n(s({variant:e}),r),...t})}export{u as B};

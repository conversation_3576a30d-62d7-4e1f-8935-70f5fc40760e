import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
	createConversation as dbCreateConversation,
	getUserConversations,
	updateConversationTitle as dbUpdateConversationTitle,
	deleteConversation as dbDeleteConversation,
	addMessage as dbAddMessage,
	getConversationWithMessages,
	deleteAllUserConversations,
	fixConversationsMode,
} from "@/db/conversations";
import { useUserStore } from "./user-store";

/**
 * 文档引用接口
 */
export interface DocReference {
	index_id: string;
	doc_name: string;
	text: string;
	page_number?: number[];
	title?: string;
	doc_id?: string;
	images?: any[];
}

/**
 * 消息接口
 * 定义单条消息的结构
 */
export interface Message {
	id: string;                    // Unique identifier for the message
	content: string;              // Content of the message
	role: "user" | "assistant";   // Role of the message sender
	timestamp: Date;             // Time when the message was created
	docReferences?: DocReference[]; // Document references for knowledge base queries
}

/**
 * 对话接口
 * 定义单个对话的结构，包含多个消息
 */
export interface Conversation {
	id: string;                  // Unique identifier for the conversation
	title: string;              // Title of the conversation
	mode: string;               // Mode of the conversation (无忧问答, 无忧分析师, 无忧计算师)
	messages: Message[];        // Array of messages in the conversation
	createdAt: Date;           // Time when the conversation was created
	updatedAt: Date;          // Time when the conversation was last updated
}

/**
 * 对话存储接口
 * 定义对话存储的所有操作方法
 */
interface ConversationStore {
	conversations: Conversation[];         // Array of all conversations
	currentConversationId: string | null; // ID of the currently active conversation
	isLoading: boolean;                   // Loading state for database operations
	error: string | null;                 // Error state

	// CRUD operations
	createConversation: (title?: string, mode?: string) => Promise<string>;      // Create a new conversation
	deleteConversation: (id: string) => Promise<void>;                          // Delete a conversation by ID
	updateConversationTitle: (id: string, title: string) => Promise<void>;     // Update conversation title
	setCurrentConversation: (id: string | null) => void;             // Set the current conversation

	// Message operations
	addMessage: (
		conversationId: string,
		content: string,
		role: "user" | "assistant",
		docReferences?: DocReference[]
	) => Promise<void>;                                                         // Add a message to a conversation
	getCurrentConversation: () => Conversation | null;                // Get the current conversation
	getConversationsByMode: (mode: string) => Conversation[];         // Get conversations filtered by mode

	// Database operations
	loadUserConversations: () => Promise<void>;                      // Load conversations from database
	syncConversationWithDB: (conversationId: string) => Promise<void>; // Sync specific conversation with database

	// Utility
	clearAllConversations: () => Promise<void>;                      // Clear all conversations
	setLoading: (loading: boolean) => void;                          // Set loading state
	setError: (error: string | null) => void;                       // Set error state
}

/**
 * 对话状态管理Store
 * 使用zustand实现的状态管理，支持持久化存储
 */
export const useConversationStore = create<ConversationStore>()(
	persist(
		(set, get) => ({
			conversations: [],
			currentConversationId: null,
			isLoading: false,
			error: null,

			/**
			 * 创建新对话
			 * @param title - 对话标题，默认为"新对话"
			 * @returns 新创建的对话ID
			 */
			createConversation: async (title = "新对话", mode = "无忧问答") => {
				// Get user from store without creating a subscription
				const userStore = useUserStore.getState();
				const user = userStore.user;
				if (!user) {
					throw new Error("用户未登录");
				}

				set({ isLoading: true, error: null });

				try {
					const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

					// Save to database
					await dbCreateConversation(user.id, { id, title, mode });

					const newConversation: Conversation = {
						id,
						title,
						mode,
						messages: [],
						createdAt: new Date(),
						updatedAt: new Date(),
					};

					set((state) => ({
						conversations: [newConversation, ...state.conversations],
						currentConversationId: id,
						isLoading: false,
					}));

					return id;
				} catch (error) {
					const errorMessage = error instanceof Error ? error.message : "创建对话失败";
					set({ error: errorMessage, isLoading: false });
					throw error;
				}
			},

			/**
			 * 删除指定对话
			 * @param id - 要删除的对话ID
			 */
			deleteConversation: async (id) => {
				const userStore = useUserStore.getState();
				const user = userStore.user;
				if (!user) {
					throw new Error("用户未登录");
				}

				set({ isLoading: true, error: null });

				try {
					// Delete from database
					await dbDeleteConversation(id, user.id);

					set((state) => {
						const filteredConversations = state.conversations.filter(
							(conv) => conv.id !== id
						);
						const newCurrentId =
							state.currentConversationId === id
								? filteredConversations.length > 0
									? filteredConversations[0].id
									: null
								: state.currentConversationId;

						return {
							conversations: filteredConversations,
							currentConversationId: newCurrentId,
							isLoading: false,
						};
					});
				} catch (error) {
					const errorMessage = error instanceof Error ? error.message : "删除对话失败";
					set({ error: errorMessage, isLoading: false });
					throw error;
				}
			},

			/**
			 * 更新对话标题
			 * @param id - 要更新的对话ID
			 * @param title - 新的对话标题
			 */
			updateConversationTitle: async (id, title) => {
				const userStore = useUserStore.getState();
				const user = userStore.user;
				if (!user) {
					throw new Error("用户未登录");
				}

				set({ isLoading: true, error: null });

				try {
					// Update in database
					await dbUpdateConversationTitle(id, user.id, title);

					set((state) => ({
						conversations: state.conversations.map((conv) =>
							conv.id === id ? { ...conv, title, updatedAt: new Date() } : conv
						),
						isLoading: false,
					}));
				} catch (error) {
					const errorMessage = error instanceof Error ? error.message : "更新标题失败";
					set({ error: errorMessage, isLoading: false });
					throw error;
				}
			},

			/**
			 * 设置当前对话
			 * @param id - 要设置为当前的对话ID，传null表示不设置任何对话为当前对话
			 */
			setCurrentConversation: (id) => {
				set({ currentConversationId: id });
			},

			/**
			 * 向指定对话添加消息
			 * @param conversationId - 对话ID
			 * @param content - 消息内容
			 * @param role - 消息发送者角色
			 * @param docReferences - 文档引用（可选）
			 */
			addMessage: async (conversationId, content, role, docReferences) => {
				set({ isLoading: true, error: null });

				try {
					const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

					// Save to database
					await dbAddMessage({
						id: messageId,
						conversationId,
						content,
						role,
						docReferences,
					});

					const newMessage: Message = {
						id: messageId,
						content,
						role,
						timestamp: new Date(),
						docReferences,
					};

					set((state) => ({
						conversations: state.conversations.map((conv) =>
							conv.id === conversationId
								? {
										...conv,
										messages: [...conv.messages, newMessage],
										updatedAt: new Date(),
									}
								: conv
						),
						isLoading: false,
					}));
				} catch (error) {
					const errorMessage = error instanceof Error ? error.message : "添加消息失败";
					set({ error: errorMessage, isLoading: false });
					throw error;
				}
			},

			/**
			 * 获取当前对话
			 * @returns 当前对话对象，如果没有则返回null
			 */
			getCurrentConversation: () => {
				const state = get();
				return (
					state.conversations.find(
						(conv) => conv.id === state.currentConversationId
					) || null
				);
			},

			/**
			 * 根据模式获取对话列表
			 */
			getConversationsByMode: (mode: string) => {
				const state = get();
				return state.conversations.filter(conv => conv.mode === mode);
			},

			/**
			 * 从数据库加载用户对话
			 */
			loadUserConversations: async () => {
				const userStore = useUserStore.getState();
				const user = userStore.user;
				if (!user) {
					set({ conversations: [], currentConversationId: null });
					return;
				}

				set({ isLoading: true, error: null });

				try {
					// 首先修复没有 mode 字段的对话
					await fixConversationsMode();

					const dbConversations = await getUserConversations(user.id);

					// Convert database conversations to store format
					const conversations: Conversation[] = await Promise.all(
						dbConversations.map(async (dbConv) => {
							const result = await getConversationWithMessages(dbConv.id, user.id);
							return {
								id: dbConv.id,
								title: dbConv.title,
								mode: dbConv.mode || '无忧问答',
								messages: result?.messages.map(msg => ({
									id: msg.id,
									content: msg.content,
									role: msg.role as "user" | "assistant",
									timestamp: new Date(msg.timestamp),
									docReferences: msg.docReferences,
								})) || [],
								createdAt: new Date(dbConv.createdAt),
								updatedAt: new Date(dbConv.updatedAt),
							};
						})
					);

					set({
						conversations,
						isLoading: false,
						// Keep current conversation if it exists in loaded conversations
						currentConversationId: get().currentConversationId &&
							conversations.find(c => c.id === get().currentConversationId)
							? get().currentConversationId
							: null
					});
				} catch (error) {
					const errorMessage = error instanceof Error ? error.message : "加载对话失败";
					set({ error: errorMessage, isLoading: false });
				}
			},

			/**
			 * 同步特定对话与数据库
			 */
			syncConversationWithDB: async (conversationId) => {
				const userStore = useUserStore.getState();
				const user = userStore.user;
				if (!user) return;

				try {
					const result = await getConversationWithMessages(conversationId, user.id);
					if (!result) return;

					const updatedConversation: Conversation = {
						id: result.conversation.id,
						title: result.conversation.title,
						mode: result.conversation.mode || '无忧问答',
						messages: result.messages.map(msg => ({
							id: msg.id,
							content: msg.content,
							role: msg.role as "user" | "assistant",
							timestamp: new Date(msg.timestamp),
							docReferences: msg.docReferences,
						})),
						createdAt: new Date(result.conversation.createdAt),
						updatedAt: new Date(result.conversation.updatedAt),
					};

					set((state) => ({
						conversations: state.conversations.map(conv =>
							conv.id === conversationId ? updatedConversation : conv
						),
					}));
				} catch (error) {
					console.error("同步对话失败:", error);
				}
			},

			/**
			 * 清空所有对话
			 */
			clearAllConversations: async () => {
				const userStore = useUserStore.getState();
				const user = userStore.user;
				if (!user) {
					set({ conversations: [], currentConversationId: null });
					return;
				}

				set({ isLoading: true, error: null });

				try {
					await deleteAllUserConversations(user.id);
					set({ conversations: [], currentConversationId: null, isLoading: false });
				} catch (error) {
					const errorMessage = error instanceof Error ? error.message : "清空对话失败";
					set({ error: errorMessage, isLoading: false });
					throw error;
				}
			},

			/**
			 * 设置加载状态
			 */
			setLoading: (loading) => {
				set({ isLoading: loading });
			},

			/**
			 * 设置错误状态
			 */
			setError: (error) => {
				set({ error });
			},
		}),
		{
			name: "conversation-store",
			partialize: (state) => ({
				currentConversationId: state.currentConversationId,
				// Don't persist conversations - they will be loaded from database
			}),
		}
	)
);
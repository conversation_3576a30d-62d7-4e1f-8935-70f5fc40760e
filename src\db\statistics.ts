import { eq } from 'drizzle-orm';
import { db } from './drizzle';
import { statistics, type Statistic, type NewStatistic } from './schema';

/**
 * Get current statistics
 * 获取当前统计信息
 */
export async function getStatistics(): Promise<Statistic | null> {
	try {
		const result = await db.select().from(statistics).limit(1);
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting statistics:", error);
		throw error;
	}
}

/**
 * Initialize statistics if not exists
 * 如果统计信息不存在则初始化
 */
export async function initializeStatistics(): Promise<Statistic> {
	try {
		const existingStats = await getStatistics();
		if (existingStats) {
			return existingStats;
		}

		const newStats: NewStatistic = {
			totalRequestTimes: 0,
			totalTokenUsage: 0,
		};

		const result = await db.insert(statistics).values(newStats).returning();
		console.log("统计信息已初始化:", result[0]);
		return result[0];
	} catch (error) {
		console.error("Error initializing statistics:", error);
		throw error;
	}
}

/**
 * Update statistics with new request and token usage
 * 更新统计信息（增加请求次数和token使用量）
 */
export async function updateStatistics(
	requestCount: number = 1,
	tokenUsed: number = 0
): Promise<Statistic | null> {
	try {
		// 确保统计信息存在
		await initializeStatistics();

		// 获取当前统计信息
		const currentStats = await getStatistics();
		if (!currentStats) {
			console.error("无法获取统计信息");
			return null;
		}

		// 计算新的统计数据
		const newTotalRequestTimes = currentStats.totalRequestTimes + requestCount;
		const newTotalTokenUsage = currentStats.totalTokenUsage + tokenUsed;

		// 更新统计信息
		const result = await db
			.update(statistics)
			.set({
				totalRequestTimes: newTotalRequestTimes,
				totalTokenUsage: newTotalTokenUsage,
			})
			.where(eq(statistics.id, currentStats.id))
			.returning();

		console.log(`统计信息已更新:`, {
			requestCount,
			tokenUsed,
			newTotalRequestTimes,
			newTotalTokenUsage
		});

		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error updating statistics:", error);
		throw error;
	}
}

/**
 * Get all statistics (for admin dashboard)
 * 获取所有统计信息（用于管理员仪表板）
 */
export async function getAllStatistics(): Promise<Statistic[]> {
	try {
		return await db.select().from(statistics);
	} catch (error) {
		console.error("Error getting all statistics:", error);
		throw error;
	}
}

/**
 * Reset statistics (admin only)
 * 重置统计信息（仅管理员）
 */
export async function resetStatistics(): Promise<Statistic | null> {
	try {
		const stats = await getStatistics();
		if (!stats) {
			return await initializeStatistics();
		}

		const result = await db
			.update(statistics)
			.set({
				totalRequestTimes: 0,
				totalTokenUsage: 0,
			})
			.where(eq(statistics.id, stats.id))
			.returning();

		console.log("统计信息已重置");
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error resetting statistics:", error);
		throw error;
	}
}

// src/router.tsx
import { createRouter as createTanStackRouter } from "@tanstack/react-router";
import { routeTree } from "./routeTree.gen";

export function createRouter() {
	const router = createTanStackRouter({
		routeTree,
		scrollRestoration: true,
		defaultNotFoundComponent: () => (
			<div className="flex flex-col items-center justify-center min-h-screen">
				<h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
				<p className="text-gray-600 mb-8">页面未找到</p>
				<a href="/" className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
					返回首页
				</a>
			</div>
		),
	});

	return router;
}

declare module "@tanstack/react-router" {
	interface Register {
		router: ReturnType<typeof createRouter>;
	}
}

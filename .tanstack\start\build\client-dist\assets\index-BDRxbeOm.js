import{r as n,j as e}from"./main-B6mr56sS.js";import{C as a,a as o,b as l,d as i,c as y}from"./card-CdUhOvNY.js";import{B as b}from"./badge-czNfS6QW.js";import{b as k,c as C}from"./user-B82y3N16.js";import"./index-_TRYHs0w.js";const B=function(){const[s,u]=n.useState(null),[j,d]=n.useState(!0),[x,f]=n.useState(null);return n.useEffect(()=>{(async()=>{try{d(!0);const[t,p]=await Promise.all([k(),C()]),r=t.length,m=t.reduce((c,N)=>c+N.requestTimes,0),g=p?.totalTokenUsage||0,h=t.filter(c=>c.isAdmin).length,v=r-h,w=r>0?Math.round(m/r):0;u({totalUsers:r,totalRequests:m,totalTokensUsed:g,adminUsers:h,regularUsers:v,averageRequestsPerUser:w})}catch(t){console.error("获取统计数据失败:",t),f("获取统计数据失败")}finally{d(!1)}})()},[]),j?e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsx("div",{className:"text-lg",children:"加载中..."})}):x?e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsx("div",{className:"text-lg text-red-500",children:x})}):e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"管理员仪表板"}),e.jsx("p",{className:"text-muted-foreground",children:"查看系统使用统计和用户数据"})]}),e.jsx(b,{variant:"secondary",className:"text-sm",children:"管理员面板"})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[e.jsxs(a,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"总用户数"}),e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:[e.jsx("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),e.jsx("circle",{cx:"9",cy:"7",r:"4"}),e.jsx("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}),e.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:s?.totalUsers||0}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["管理员: ",s?.adminUsers||0," | 普通用户: ",s?.regularUsers||0]})]})]}),e.jsxs(a,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"总请求次数"}),e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:e.jsx("path",{d:"M22 12h-4l-3 9L9 3l-3 9H2"})})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:s?.totalRequests.toLocaleString()||0}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["平均每用户: ",s?.averageRequestsPerUser||0," 次"]})]})]}),e.jsxs(a,{children:[e.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Token使用量"}),e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:e.jsx("path",{d:"M12 2v20m8-10H4"})})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:s?.totalTokensUsed.toLocaleString()||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"实际使用量统计"})]})]})]}),e.jsxs(a,{children:[e.jsxs(o,{children:[e.jsx(l,{children:"快速操作"}),e.jsx(y,{children:"管理系统用户和查看详细数据"})]}),e.jsx(i,{className:"flex gap-4",children:e.jsx("a",{href:"/dashboard/user",className:"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2",children:"用户管理"})})]})]})};export{B as component};

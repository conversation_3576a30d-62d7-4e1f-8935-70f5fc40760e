import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import {
	getOrCreateUser,
	updateUser,
	getUserByDingTalkUnionId,
	createUserWithDingTalkInfo,
} from "@/db";
import { useUserStore } from "@/stores/user-store";

/**
 * 通过后端API代理调用钉钉API获取用户token
 * @param code 授权码
 * @param clientId 应用ID
 * @param clientSecret 应用密钥
 * @returns 用户token信息
 */
async function getDingTalkUserToken(
	code: string,
	clientId: string,
	clientSecret: string
) {
	try {
		// 调用我们的后端API代理，避免CORS问题
		const response = await fetch("/api/auth/dingtalk-callback", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				clientId,
				clientSecret,
				code,
				grantType: "authorization_code",
			}),
		});

		const data = await response.json();

		if (!response.ok) {
			console.error("API代理调用失败:", data);
			throw new Error(
				data.message || `API调用失败: ${response.status} ${response.statusText}`
			);
		}

		return data;
	} catch (error) {
		console.error("调用钉钉API获取用户token失败:", error);
		// 如果API调用失败，返回null，使用fallback逻辑
		return null;
	}
}

/**
 * 通过后端API代理调用钉钉API获取用户信息
 * @param accessToken 用户访问令牌
 * @param unionId 用户unionId，默认为'me'获取当前用户信息
 * @returns 用户信息
 */
async function getDingTalkUserInfo(
	accessToken: string,
	unionId: string = "me"
) {
	try {
		// 调用我们的后端API代理，避免CORS问题
		const response = await fetch("/api/auth/dingtalk-userinfo", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				accessToken,
				unionId,
			}),
		});

		const data = await response.json();

		if (!response.ok) {
			console.error("获取钉钉用户信息失败:", data);
			throw new Error(
				data.error ||
					`获取用户信息失败: ${response.status} ${response.statusText}`
			);
		}

		return data;
	} catch (error) {
		console.error("调用钉钉用户信息API失败:", error);
		// 如果API调用失败，返回null
		return null;
	}
}

/**
 * 通过后端API代理获取钉钉用户个人信息
 * @param accessToken 用户访问令牌
 * @param unionId 用户unionId，默认为'me'获取当前用户信息
 * @returns 用户个人信息
 */
/**
 * 钉钉登录回调页面
 * 处理钉钉OAuth授权后的回调逻辑
 */
export const Route = createFileRoute("/auth/dingtalk/callback")({
	component: DingTalkCallback,
});

function DingTalkCallback() {
	const navigate = useNavigate();
	const { login } = useAuth();
	const [status, setStatus] = useState<"loading" | "success" | "error">(
		"loading"
	);
	const [message, setMessage] = useState("正在处理钉钉登录...");

	useEffect(() => {
		const handleDingTalkCallback = async () => {
			try {
				// 获取URL参数
				const urlParams = new URLSearchParams(window.location.search);
				const code = urlParams.get("code") || urlParams.get("authCode");
				const state = urlParams.get("state");

				if (!code) {
					throw new Error("未获取到授权码");
				}

				if (state !== "dingtalk_login") {
					throw new Error("状态参数验证失败");
				}

				// 钉钉应用配置（从环境变量获取）
				const clientId =
					import.meta.env.VITE_DINGTALK_CLIENT_ID || "your_client_id";
				const clientSecret =
					import.meta.env.VITE_DINGTALK_CLIENT_SECRET || "your_client_secret";

				// 打印所有环境变量和配置信息
				console.log("=== 钉钉配置调试信息 ===");
				console.log(
					"环境变量 VITE_DINGTALK_CLIENT_ID:",
					import.meta.env.VITE_DINGTALK_CLIENT_ID
				);
				console.log(
					"环境变量 VITE_DINGTALK_CLIENT_SECRET:",
					import.meta.env.VITE_DINGTALK_CLIENT_SECRET
				);
				console.log("实际使用的 clientId:", clientId);
				console.log("实际使用的 clientSecret:", clientSecret);
				console.log("clientId 长度:", clientId?.length);
				console.log("clientSecret 长度:", clientSecret?.length);
				console.log("所有环境变量:", import.meta.env);

				if (!clientId || !clientSecret || clientId === "your_client_id") {
					console.error("钉钉应用配置检查失败:", {
						clientId: clientId,
						clientSecret: clientSecret,
						hasClientId: !!clientId,
						hasClientSecret: !!clientSecret,
						isDefaultClientId: clientId === "your_client_id",
					});
					throw new Error("钉钉应用配置不完整，请检查环境变量");
				}

				// 打印授权码
				console.log("收到钉钉授权码 (authCode):", code);

				// 打印钉钉登录获取到的数据
				const dingTalkData = {
					code,
					state,
					timestamp: new Date().toISOString(),
					clientId,
					clientSecret: clientSecret
						? `${clientSecret.substring(0, 4)}****${clientSecret.substring(clientSecret.length - 4)}`
						: "undefined",
					redirectUri: window.location.origin + "/auth/dingtalk/callback",
				};
				console.log("钉钉登录数据:", dingTalkData);

				// 调用钉钉API获取用户token
				const tokenResponse = await getDingTalkUserToken(
					code,
					clientId,
					clientSecret
				);
				console.log("钉钉用户token响应:", tokenResponse);

				// 根据token响应生成用户ID和token
				let userID: string;
				let userToken: string;
				let userInfo: any = null;

				if (tokenResponse && (tokenResponse.accessToken || tokenResponse.fallback)) {
					if (tokenResponse.fallback) {
						// 使用fallback token
						console.warn("使用fallback token，钉钉API连接失败");
						userToken = tokenResponse.fallback.accessToken;
						userID = `dingtalk_fallback_${code.substring(0, 8)}`;

						console.log("使用fallback token:", {
							accessToken: userToken,
							reason: tokenResponse.message,
							suggestions: tokenResponse.suggestions
						});

						// 创建fallback用户信息
						userInfo = {
							nick: "钉钉用户",
							unionId: userID,
							openId: `fallback_${code.substring(0, 8)}`,
							mobile: "未设置",
							avatarUrl: null
						};
					} else {
						// 使用钉钉返回的真实token
						userToken = tokenResponse.accessToken;

						console.log("使用钉钉API返回的token:", {
							accessToken: userToken,
							refreshToken: tokenResponse.refreshToken,
							expireIn: tokenResponse.expireIn,
							corpId: tokenResponse.corpId,
						});

						// 获取用户个人信息
						userInfo = await getDingTalkUserInfo(userToken);
						console.log("钉钉用户个人信息:", userInfo);

						// 获取钉钉unionId，如果没有则使用corpId或授权码
						if (userInfo && userInfo.unionId) {
							// 直接使用unionId
							userID = userInfo.unionId;
						} else if (tokenResponse.corpId) {
							userID = `dingtalk_${tokenResponse.corpId}`;
						} else {
							userID = `dingtalk_${code.substring(0, 8)}`;
						}
					}
				} else {
					// 如果API调用失败，使用fallback逻辑
					console.warn("钉钉API调用失败，使用fallback token");
					userID = `dingtalk_${code.substring(0, 8)}`;
					userToken = `dingtalk_fallback_${code}`;
				}

				// 查询数据库中是否存在该用户
				let dbUser;
				try {
					// 通过dingTalkUnionId查询用户
					console.log("正在通过dingTalkUnionId查询用户:", userID);
					dbUser = await getUserByDingTalkUnionId(userID);

					if (dbUser) {
						console.log("用户已存在:", dbUser);
						// 如果获取到了新的用户信息，更新数据库中的用户信息
						if (userInfo) {
							const updateData = {
								name: userInfo.nick || dbUser.name,
								mobile: userInfo.mobile || dbUser.mobile,
								avatar: userInfo.avatarUrl || dbUser.avatar,
								dingTalkUserId: userInfo.openId || dbUser.dingTalkUserId,
								updatedAt: new Date().toISOString(),
							};
							console.log("正在更新用户信息:", updateData);
							try {
								await updateUser(dbUser.dingTalkUnionId, updateData);
								console.log("用户信息更新成功");
								// 更新本地用户对象
								dbUser = { ...dbUser, ...updateData };
							} catch (updateError) {
								console.error("更新用户信息失败:", updateError);
							}
						}
					} else {
						console.log("用户不存在，正在创建新用户...");
						// 一次性创建包含完整钉钉信息的用户
						const dingTalkInfo = userInfo
							? {
									dingTalkUserId: userInfo.openId,
									dingTalkUnionId: userInfo.unionId || userID,
									name: userInfo.nick,
									avatar: userInfo.avatarUrl,
									mobile: userInfo.mobile,
							  }
							: {
									dingTalkUnionId: userID,
									name: "钉钉用户",
									mobile: "未设置"
							  };

						dbUser = await createUserWithDingTalkInfo(dingTalkInfo);
						console.log("新用户创建成功:", dbUser);
					}
				} catch (dbError) {
					console.error("数据库操作失败:", dbError);
					// 如果数据库操作失败，使用临时用户数据，优先使用钉钉返回的信息
					dbUser = {
						id: Date.now(),
						dingTalkUnionId: userInfo?.unionId || userID,
						isAdmin: false,
						token: 20000,
						requestTimes: 0,
						name: userInfo?.nick || "钉钉用户",
						mobile: userInfo?.mobile || "未设置",
						avatar: userInfo?.avatarUrl,
						dingTalkUserId: userInfo?.openId,
						createdAt: new Date().toISOString(),
						updatedAt: new Date().toISOString(),
					};
				}

				// 调用登录方法，使用获取到的真实token
				// 将用户信息存储到localStorage中，供其他组件使用
				if (userInfo) {
					localStorage.setItem("dingtalk_user_info", JSON.stringify(userInfo));
				}

				// 将用户信息存储到用户store中
				useUserStore.getState().setUser(dbUser);

				login(dbUser, userToken);

				setStatus("success");
				setMessage("登录成功，正在跳转...");

				// 根据用户角色决定跳转页面
				let redirectPath = localStorage.getItem("redirectAfterLogin");
				
				// 如果没有预设的重定向路径，根据用户角色决定默认页面
				if (!redirectPath) {
					// 管理员用户跳转到dashboard，普通用户跳转到AI页面
					redirectPath = dbUser.isAdmin ? "/dashboard" : "/ai";
				}
				
				// 清除重定向路径
				localStorage.removeItem("redirectAfterLogin");

				// 跳转到目标页面
				setTimeout(() => {
					navigate({ to: redirectPath });
				}, 1500);
			} catch (error) {
				console.error("钉钉登录失败:", error);
				setStatus("error");
				setMessage(error instanceof Error ? error.message : "登录失败，请重试");

				// 3秒后跳转回登录页
				setTimeout(() => {
					navigate({ to: "/auth/login" });
				}, 3000);
			}
		};

		handleDingTalkCallback();
	}, []);

	return (
		<div className="min-h-screen w-full flex items-center justify-center bg-white">
			<div className="w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl p-8 flex flex-col items-center border border-blue-100">
				{/* 状态图标 */}
				<div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-white mb-6 shadow-lg">
					{status === "loading" && (
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
					)}
					{status === "success" && (
						<svg
							className="w-8 h-8 text-green-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M5 13l4 4L19 7"
							/>
						</svg>
					)}
					{status === "error" && (
						<svg
							className="w-8 h-8 text-red-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					)}
				</div>

				{/* 状态信息 */}
				<h2 className="text-xl font-semibold mb-2 text-center text-gray-800">
					{status === "loading" && "处理中"}
					{status === "success" && "登录成功"}
					{status === "error" && "登录失败"}
				</h2>

				<p className="text-gray-600 text-sm text-center">{message}</p>

				{status === "error" && (
					<button
						onClick={() => navigate({ to: "/auth/login" })}
						className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
					>
						返回登录
					</button>
				)}
			</div>
		</div>
	);
}

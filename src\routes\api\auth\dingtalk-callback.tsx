import { createServerFileRoute } from "@tanstack/react-start/server";
import { json } from "@tanstack/react-start";

/**
 * 钉钉获取用户AccessToken的API代理
 * 解决前端直接调用钉钉API的CORS问题
 */
export const ServerRoute = createServerFileRoute(
	"/api/auth/dingtalk-callback"
).methods({
	POST: async ({ request }) => {
		try {
			const body = await request.json();
			const { clientId, clientSecret, code, grantType } = body;

			// 打印接收到的参数（用于调试）
			console.log("=== API代理接收到的参数 ===");
			console.log("clientId:", clientId);
			console.log("clientSecret:", clientSecret ? `${clientSecret.substring(0, 4)}****${clientSecret.substring(clientSecret.length - 4)}` : 'undefined');
			console.log("code:", code);
			console.log("grantType:", grantType);
			console.log("clientId 长度:", clientId?.length);
			console.log("clientSecret 长度:", clientSecret?.length);
			console.log("code 长度:", code?.length);

			// 验证必需参数
			if (!clientId || !clientSecret || !code || !grantType) {
				console.error("参数验证失败:", {
					clientId: !!clientId,
					clientSecret: !!clientSecret,
					code: !!code,
					grantType: !!grantType
				});
				return json(
					{
						error: "Missing required parameters",
						message: "缺少必需的参数：clientId, clientSecret, code, grantType",
						details: {
							clientId: !!clientId,
							clientSecret: !!clientSecret,
							code: !!code,
							grantType: !!grantType
						}
					},
					{
						status: 400,
					}
				);
			}

			// 准备发送给钉钉API的数据
			const requestData = {
				clientId,
				clientSecret,
				code,
				grantType,
			};

			console.log("=== 发送给钉钉API的数据 ===");
			console.log("请求URL: https://api.dingtalk.com/v1.0/oauth2/userAccessToken");
			console.log("请求数据:", {
				...requestData,
				clientSecret: clientSecret ? `${clientSecret.substring(0, 4)}****${clientSecret.substring(clientSecret.length - 4)}` : 'undefined'
			});

			// 网络连接测试
			console.log("=== 开始网络连接测试 ===");
			try {
				const testResponse = await fetch("https://www.baidu.com", {
					method: "HEAD",
					signal: AbortSignal.timeout(5000)
				});
				console.log("网络连接测试成功，状态码:", testResponse.status);
			} catch (testError) {
				console.error("网络连接测试失败:", testError);
			}

			// 调用钉钉API
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

			try {
				const response = await fetch(
					"https://api.dingtalk.com/v1.0/oauth2/userAccessToken",
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							"Accept": "application/json",
							"User-Agent": "Mozilla/5.0 (compatible; DingTalk-OAuth-Client/1.0)",
						},
						body: JSON.stringify(requestData),
						signal: controller.signal,
					}
				);

				clearTimeout(timeoutId);

				if (!response.ok) {
					const errorText = await response.text();
					console.error("=== 钉钉API HTTP错误 ===");
					console.error("HTTP状态:", response.status, response.statusText);
					console.error("错误响应文本:", errorText);

					let errorData;
					try {
						errorData = JSON.parse(errorText);
					} catch {
						errorData = { message: errorText };
					}

					return json(
						{
							error: "DingTalk API Error",
							message: errorData.message || "钉钉API调用失败",
							details: errorData,
							httpStatus: response.status,
							httpStatusText: response.statusText
						},
						{
							status: response.status,
						}
					);
				}

				const data = await response.json();

				console.log("=== 钉钉API响应信息 ===");
				console.log("响应状态码:", response.status);
				console.log("响应状态文本:", response.statusText);
				console.log("响应头:", Object.fromEntries(response.headers.entries()));
				console.log("响应数据:", data);

				console.log("=== 钉钉API调用成功 ===");
				console.log("成功响应数据:", data);
				// 返回成功响应
				return json(data);

			} catch (fetchError) {
				clearTimeout(timeoutId);

				if (fetchError.name === 'AbortError') {
					console.error("=== 钉钉API请求超时 ===");
					console.error("请求超时，可能是网络问题或钉钉服务器响应慢");
					return json(
						{
							error: "Request Timeout",
							message: "请求钉钉API超时，请检查网络连接或稍后重试",
							details: { timeout: "30秒" }
						},
						{
							status: 408,
						}
					);
				}

				console.error("=== 钉钉API网络错误 ===");
				console.error("网络错误详情:", fetchError);
				throw fetchError; // 重新抛出错误，让外层catch处理
			}
		} catch (error) {
			console.error("API代理错误:", error);

			// 如果是网络连接错误，提供更详细的错误信息和建议
			if (error instanceof Error && error.message.includes('fetch failed')) {
				console.log("=== 网络连接失败，提供fallback方案 ===");
				return json(
					{
						error: "Network Connection Failed",
						message: "无法连接到钉钉API服务器，可能的原因：1. 网络连接问题 2. 防火墙阻止 3. 钉钉服务器暂时不可用",
						suggestions: [
							"检查网络连接",
							"确认防火墙设置",
							"稍后重试",
							"联系系统管理员"
						],
						fallback: {
							// 提供一个临时的token用于测试
							accessToken: `fallback_${code}_${Date.now()}`,
							refreshToken: `refresh_fallback_${code}_${Date.now()}`,
							expireIn: 7200,
							corpId: "fallback_corp"
						},
						details: error.message
					},
					{
						status: 503, // Service Unavailable
					}
				);
			}

			return json(
				{
					error: "Internal Server Error",
					message: error instanceof Error ? error.message : "服务器内部错误",
				},
				{
					status: 500,
				}
			);
		}
	},

	// 处理预检请求
	OPTIONS: async () => {
		return new Response(null, {
			status: 200,
			headers: {
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			},
		});
	},
});

import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'

/**
 * 钉钉获取用户信息API代理
 * 解决前端直接调用钉钉API的CORS问题
 */
export const ServerRoute = createServerFileRoute('/api/auth/dingtalk-userinfo')
  .methods({
    POST: async ({ request }) => {
      try {
        const body = await request.json()
        const { accessToken, unionId = 'me' } = body

        // 验证必需参数
        if (!accessToken) {
          return json(
            { error: 'Missing required parameter: accessToken' },
            { status: 400 }
          )
        }

        // 调用钉钉API获取用户信息
        const response = await fetch(
          `https://api.dingtalk.com/v1.0/contact/users/${unionId}`,
          {
            method: 'GET',
            headers: {
              'x-acs-dingtalk-access-token': accessToken,
              'Content-Type': 'application/json',
            },
          }
        )

        const data = await response.json()

        if (!response.ok) {
          console.error('钉钉用户信息API调用失败:', data)
          return json(
            {
              error: '钉钉用户信息API调用失败',
              details: data,
              status: response.status,
            },
            { status: response.status }
          )
        }

        // 返回用户信息
        return json(data, {
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, x-acs-dingtalk-access-token',
          },
        })
      } catch (error) {
        console.error('钉钉用户信息API代理错误:', error)
        return json(
          {
            error: '服务器内部错误',
            details: error instanceof Error ? error.message : String(error),
          },
          { status: 500 }
        )
      }
    },
    OPTIONS: async () => {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, x-acs-dingtalk-access-token',
        },
      })
    },
  })

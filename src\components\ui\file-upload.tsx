import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useImageUpload } from "@/components/hooks/use-image-upload";
import { ImagePlus, X, Upload, Trash2 } from "lucide-react";
import { useCallback, useState } from "react";
import { cn } from "@/lib/utils";

/**
 * 文件上传组件属性接口
 */
interface FileUploadProps {
	onFileSelect?: (file: File | null) => void;
	fileContent?: string; // 文件内容，用于在预览框中显示
}

/**
 * 文件上传组件
 * 支持拖拽上传和点击上传功能
 */
export function FileUpload({ onFileSelect, fileContent }: FileUploadProps) {
	const {
		previewUrl,
		fileName,
		fileInputRef,
		handleThumbnailClick,
		handleFileChange: originalHandleFileChange,
		handleRemove: originalHandleRemove,
	} = useImageUpload({
		onUpload: (url) => console.log("Uploaded image URL:", url),
	});

	const [isDragging, setIsDragging] = useState(false);

	// 包装文件变化处理函数，添加文件选择回调
	const handleFileChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			originalHandleFileChange(event);
			const file = event.target.files?.[0] || null;
			onFileSelect?.(file);
		},
		[originalHandleFileChange, onFileSelect]
	);

	// 包装文件移除处理函数，添加文件选择回调
	const handleRemove = useCallback(() => {
		originalHandleRemove();
		onFileSelect?.(null);
	}, [originalHandleRemove, onFileSelect]);

	/**
	 * 处理拖拽悬停
	 */
	const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
	};

	/**
	 * 处理拖拽进入
	 */
	const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	};

	/**
	 * 处理拖拽离开
	 */
	const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
	};

	/**
	 * 处理文件拖拽放置
	 */
	const handleDrop = useCallback(
		(e: React.DragEvent<HTMLDivElement>) => {
			e.preventDefault();
			e.stopPropagation();
			setIsDragging(false);

			const file = e.dataTransfer.files?.[0];
			if (file) {
				// 直接处理文件，避免创建假的事件对象
				if (fileInputRef.current) {
					// 创建一个新的FileList对象
					const dataTransfer = new DataTransfer();
					dataTransfer.items.add(file);
					fileInputRef.current.files = dataTransfer.files;

					// 触发change事件
					const event = new Event('change', { bubbles: true });
					fileInputRef.current.dispatchEvent(event);
				}
				// 同时触发文件选择回调
				onFileSelect?.(file);
			}
		},
		[fileInputRef, onFileSelect]
	);

	return (
		<div className="w-full max-w-md space-y-6 rounded-xl border border-border bg-card p-6 shadow-sm">
			<div className="space-y-2">
				<h3 className="text-lg font-medium">文件上传</h3>
				<p className="text-sm text-muted-foreground">
					支持格式: TXT, PDF, DOCX
				</p>
			</div>

			<Input
				type="file"
				accept=".txt,.pdf,.docx,text/plain,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
				className="hidden"
				ref={fileInputRef}
				onChange={handleFileChange}
			/>

			{!fileName ? (
				<div
					onClick={handleThumbnailClick}
					onDragOver={handleDragOver}
					onDragEnter={handleDragEnter}
					onDragLeave={handleDragLeave}
					onDrop={handleDrop}
					className={cn(
						"flex h-64 cursor-pointer flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/50 transition-colors hover:bg-muted",
						isDragging && "border-primary/50 bg-primary/5"
					)}
				>
					<div className="rounded-full bg-background p-3 shadow-sm">
						<ImagePlus className="h-6 w-6 text-muted-foreground" />
					</div>
					<div className="text-center">
						<p className="text-sm font-medium">点击选择文件</p>
						<p className="text-xs text-muted-foreground">
							或拖拽文件到此处
						</p>
					</div>
				</div>
			) : (
				<div className="relative">
					<div className="relative h-64 overflow-hidden rounded-lg border bg-background">
						{/* 文件内容预览区域 */}
						<div className="h-full w-full p-4 overflow-auto">
							{fileContent ? (
								<div className="text-sm text-foreground whitespace-pre-wrap font-mono leading-relaxed">
									{fileContent}
								</div>
							) : (
								<div className="flex h-full items-center justify-center text-muted-foreground">
									<div className="text-center">
										<p className="text-sm font-medium">Preview</p>
										<p className="text-xs">文件内容将在这里显示</p>
									</div>
								</div>
							)}
						</div>
					</div>
					{fileName && (
						<div className="mt-2 flex items-center gap-2 text-sm text-muted-foreground">
							<span className="truncate">{fileName}</span>
							<button
								onClick={handleRemove}
								className="ml-auto rounded-full p-1 hover:bg-muted"
							>
								<X className="h-4 w-4" />
							</button>
						</div>
					)}
				</div>
			)}
		</div>
	);
}
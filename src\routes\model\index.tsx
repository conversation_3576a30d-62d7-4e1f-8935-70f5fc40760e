import { createFileRoute, Link } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookOpen, Zap, Target, Sparkles, ArrowLeft } from 'lucide-react'

export const Route = createFileRoute('/model/')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="mb-4">
        <Link to="/">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </Link>
      </div>
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">模型中心</h1>
        <p className="text-muted-foreground">探索中化集团先进的AI模型解决方案</p>
      </div>

      {/* Sinochem Xinghai Model Introduction */}
      <Card className="mb-8 border-2">
        <CardHeader>
          <div className="flex items-center gap-2 mb-2">
            <Sparkles className="h-5 w-5 text-primary" />
            <Badge variant="default" className="text-sm">
              推荐模型
            </Badge>
          </div>
          <CardTitle className="text-2xl">Sinochem Xinghai 兴海大模型</CardTitle>
          <CardDescription>
            中化兴海自主研发的企业级大语言模型，专为化工行业智能化转型而设计
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Target className="h-4 w-4" />
                核心能力
              </h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• 化工领域专业知识理解与生成</li>
                <li>• 安全规程智能分析与预警</li>
                <li>• 生产流程优化建议</li>
                <li>• 设备故障诊断与预测</li>
                <li>• 供应链智能决策支持</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                技术特色
              </h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• 千亿级参数规模，行业知识深度训练</li>
                <li>• 多模态融合，支持文本、图像、数据</li>
                <li>• 实时学习与知识更新机制</li>
                <li>• 企业级安全与合规保障</li>
                <li>• 私有化部署与API服务双模式</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 pt-6 border-t">
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary">化工安全</Badge>
              <Badge variant="secondary">生产优化</Badge>
              <Badge variant="secondary">质量管控</Badge>
              <Badge variant="secondary">供应链</Badge>
              <Badge variant="secondary">研发创新</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

    
    </div>
  )
}

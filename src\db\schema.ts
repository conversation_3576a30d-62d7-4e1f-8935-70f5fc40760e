import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// 用户表
export const users = sqliteTable('users', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	// 使用dingTalkUnionId作为主要标识（可选，用于钉钉登录）
	dingTalkUnionId: text('dingTalkUnionId'),
	// 邮箱密码登录字段
	email: text('email'),
	password: text('password'),
	// 用户基本信息
	isAdmin: integer('isAdmin', { mode: 'boolean' }).notNull().default(false),
	token: integer('token').notNull().default(0),
	requestTimes: integer('requestTimes').notNull().default(0),
	dingTalkUserId: text('dingTalkUserId'),
	name: text('name'),
	avatar: text('avatar'),
	mobile: text('mobile'),
	createdAt: text('createdAt').notNull().default(sql`(datetime('now'))`),
	updatedAt: text('updatedAt').notNull().default(sql`(datetime('now'))`),
});




// 导出类型
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

// 统计信息表
export const statistics = sqliteTable('statistics', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	totalRequestTimes: integer('totalRequestTimes').notNull().default(0),
	totalTokenUsage: integer('totalTokenUsage').notNull().default(0),
});

// 导出类型
export type Statistic = typeof statistics.$inferSelect;
export type NewStatistic = typeof statistics.$inferInsert;

// 对话表
export const conversations = sqliteTable('conversations', {
	id: text('id').primaryKey(), // 使用字符串ID，与前端保持一致
	userId: integer('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
	title: text('title').notNull(),
	mode: text('mode', { enum: ['无忧问答', '无忧分析师', '无忧计算师'] }).notNull().default('无忧问答'), // 对话模式
	createdAt: text('createdAt').notNull().default(sql`(datetime('now'))`),
	updatedAt: text('updatedAt').notNull().default(sql`(datetime('now'))`),
});

// 消息表
export const messages = sqliteTable('messages', {
	id: text('id').primaryKey(), // 使用字符串ID，与前端保持一致
	conversationId: text('conversationId').notNull().references(() => conversations.id, { onDelete: 'cascade' }),
	content: text('content').notNull(),
	role: text('role', { enum: ['user', 'assistant'] }).notNull(),
	timestamp: text('timestamp').notNull().default(sql`(datetime('now'))`),
	docReferences: text('docReferences'), // 存储文档引用的JSON字符串
});

// 导出类型
export type Conversation = typeof conversations.$inferSelect;
export type NewConversation = typeof conversations.$inferInsert;
export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;
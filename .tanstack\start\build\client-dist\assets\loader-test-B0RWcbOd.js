import{j as e,r as i}from"./main-B6mr56sS.js";import{L as s}from"./loader-one-DvFwIHNv.js";import{B as d}from"./button-B5f_3GyS.js";import"./index-_TRYHs0w.js";function c(){return e.jsx(s,{})}const p=function(){const[l,t]=i.useState(!1),[a,r]=i.useState(!1),n=()=>{r(!0),setTimeout(()=>{r(!1)},3e3)};return e.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"LoaderOne 组件测试"}),e.jsx("p",{className:"text-gray-600",children:"测试对话期间等待响应的 loader 动画效果"})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"基础 LoaderOne 组件"}),e.jsx("div",{className:"flex items-center justify-center py-8 bg-gray-50 rounded-lg",children:e.jsx(s,{})})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"LoaderDemo 组件"}),e.jsx("div",{className:"flex items-center justify-center py-8 bg-gray-50 rounded-lg",children:e.jsx(c,{})})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"交互式测试"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsx(d,{onClick:()=>t(!l),variant:l?"destructive":"default",children:l?"停止 Loader":"启动 Loader"}),e.jsx(d,{onClick:n,disabled:a,variant:"outline",children:a?"API 调用中...":"模拟 API 调用 (3秒)"})]}),e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"当前状态:"}),l||a?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(s,{}),e.jsx("span",{className:"text-sm",children:a?"正在调用 API...":"手动启动的 Loader"})]}):e.jsx("span",{className:"text-sm text-gray-500",children:"无加载状态"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"聊天气泡中的使用示例"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-end",children:e.jsx("div",{className:"bg-blue-500 text-white rounded-lg px-4 py-2 max-w-xs",children:"你好，请帮我分析一下这个文档"})}),e.jsx("div",{className:"flex justify-start",children:e.jsx("div",{className:"bg-gray-100 rounded-lg px-4 py-2 max-w-xs",children:e.jsx("div",{className:"flex items-center justify-center py-2",children:e.jsx(s,{})})})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"按钮中的使用示例"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{disabled:!0,className:"w-full",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(s,{}),e.jsx("span",{children:"分析中..."})]})}),e.jsx(d,{disabled:!0,variant:"outline",className:"w-full",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(s,{}),e.jsx("span",{children:"上传中..."})]})})]})]}),e.jsxs("div",{className:"bg-blue-50 rounded-lg p-6 border border-blue-200",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4 text-blue-900",children:"使用说明"}),e.jsxs("div",{className:"space-y-2 text-blue-800",children:[e.jsxs("p",{children:["• ",e.jsx("strong",{children:"基础使用:"})," 直接导入 LoaderOne 组件并使用"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"聊天场景:"})," 在等待 API 响应时显示在聊天气泡中"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"按钮场景:"})," 在按钮中显示加载状态"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"动画效果:"})," 三个蓝色圆点的跳动动画，循环播放"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"响应式:"})," 自适应容器大小"]})]})]})]})})};export{p as component};
